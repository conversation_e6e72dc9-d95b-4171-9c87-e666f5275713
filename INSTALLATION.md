# تعليمات التثبيت - CB Killfeed المطور

## متطلبات التشغيل
- FiveM Server
- ESX أو أي Framework (اختياري)

## خطوات التثبيت

### 1. رفع الملفات
1. ضع مجلد `CB_killfeed` في مجلد `resources`
2. تأكد من وجود جميع الملفات:
   - `client.lua`
   - `server.lua`
   - `config.lua`
   - `fxmanifest.lua`
   - مجلد `html` مع جميع محتوياته

### 2. إضافة السكريبت للسيرفر
أضف هذا السطر في ملف `server.cfg`:
```
start CB_killfeed
```

### 3. إعادة تشغيل السيرفر
أعد تشغيل السيرفر أو استخدم الأمر:
```
restart CB_killfeed
```

## التحقق من التثبيت

### 1. رسائل البدء
عند دخول السيرفر، يجب أن تظهر رسائل:
- "تم تحميل الـ Killfeed المطور بنجاح!"
- "استخدم الأمر / أو /killsettings لفتح قائمة الإعدادات"

### 2. اختبار الأوامر
جرب الأوامر التالية:
- `/killfeed` - لتشغيل/إيقاف الـ killfeed
- `/` - لفتح قائمة الإعدادات
- `/killsettings` - لفتح قائمة الإعدادات

### 3. اختبار الـ Killfeed
- اقتل شخصاً أو اطلب من شخص قتلك
- يجب أن يظهر الـ kill مع المسافة
- جرب تغيير الألوان من القائمة

## الإعدادات الأساسية

### في ملف `config.lua`:
```lua
Config.showTime = 3000 -- مدة عرض الـ kill (بالميلي ثانية)
Config.maxKillLines = 8 -- عدد الأسطر الأقصى
Config.showKillDist = true -- عرض المسافة
Config.ShowHideCommand = "killfeed" -- أمر التشغيل/الإيقاف
```

## استكشاف الأخطاء

### المشكلة: لا يظهر الـ Killfeed
**الحل:**
1. تأكد من تشغيل السكريبت: `restart CB_killfeed`
2. تحقق من ملف `fxmanifest.lua`
3. استخدم أمر `/killfeed` للتأكد من التشغيل

### المشكلة: لا تفتح قائمة الإعدادات
**الحل:**
1. تأكد من وجود ملفات HTML
2. جرب الأمر `/killsettings` بدلاً من `/`
3. تحقق من Console للأخطاء

### المشكلة: لا تظهر المسافة
**الحل:**
1. تأكد من أن `Config.showKillDist = true`
2. المسافة تظهر فقط بين اللاعبين
3. جرب أسلحة مختلفة

### المشكلة: الصور المخصصة لا تعمل
**الحل:**
1. تأكد من أن الصورة أقل من 2MB
2. استخدم صيغ صور مدعومة (PNG, JPG, GIF)
3. جرب إعادة رفع الصورة

## الدعم الفني

إذا واجهت مشاكل:
1. تحقق من Console للأخطاء
2. تأكد من إصدار FiveM محدث
3. تواصل مع الدعم الفني

---

**CB STORE - Discord: https://discord.gg/CBS**
