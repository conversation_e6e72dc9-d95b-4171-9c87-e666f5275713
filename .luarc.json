{"diagnostics": {"globals": ["AddEventHandler", "RegisterNetEvent", "RegisterCommand", "RegisterNUICallback", "Citizen", "SendNUIMessage", "SetNuiFocus", "TriggerEvent", "TriggerServerEvent", "TriggerClientEvent", "GetGameBuildNumber", "PlayerPedId", "PedToNet", "IsPedAPlayer", "IsPedMale", "GetPlayerServerId", "NetworkGetPlayerIndexFromPed", "GetPedType", "GetPedSubType", "GetEntityCoords", "GetEntityHeading", "DoesEntityExist", "IsEntityAPed", "GetVehiclePedIsIn", "GetVehicleClass", "IsPedShooting", "GetPedLastDamageBone", "IsFirstPersonAimCamActive", "NetworkHasControlOfEntity", "FindFirstVehicle", "FindNextVehicle", "EndFindVehicle", "SetPedAsNoLongerNeeded", "DeleteEntity", "Wait", "CreatePed", "SetBlockingOfNonTemporaryEvents", "GetHashKey", "RequestModel", "HasModelLoaded", "SetPedAlertness", "HasEntityBeenDamagedByEntity", "GetPedInVehicleSeat", "GetOffsetFromEntityInWorldCoords", "NetworkGetEntityFromNetworkId", "GetPlayerName", "GetEntityModel", "GetPlayers", "PerformHttpRequest", "json", "Config", "fx_version", "game", "lua54", "author", "description", "version", "ui_page", "shared_scripts", "client_script", "server_script", "escrow_ignore", "files"]}, "workspace": {"checkThirdParty": false}}